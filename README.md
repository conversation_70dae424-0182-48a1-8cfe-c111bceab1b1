# Interview AI - React + Supabase

Aplikasi Interview AI yang dibangun dengan React dan Supabase untuk membantu pengguna berlatih wawancara kerja dengan fitur perekaman suara dan manajemen sesi interview.

## Fitur Utama

- **Autentikasi Pengguna**: Login dan registrasi dengan Supabase Auth
- **Dashboard Interview**: <PERSON><PERSON><PERSON> semua sesi interview yang telah dibuat
- **Sesi Interview Interaktif**: <PERSON><PERSON><PERSON> pertanyaan interview dengan teks atau suara
- **Perekaman Suara**: Menggunakan Web Speech API untuk input suara
- **Manajemen Respons**: Menyimpan dan mengedit jawaban interview
- **Hasil Interview**: Melihat ringkasan dan jawaban dari sesi interview yang telah selesai
- **Row Level Security**: Keamanan data dengan RLS policies di Supabase

## Teknologi yang Digunakan

- **Frontend**: React 19, Material-UI, React Router
- **Backend**: Supabase (Database, Auth, Real-time)
- **Speech Recognition**: react-speech-recognition
- **Build Tool**: Vite

## Struktur Database

### Tables:
1. **users** - <PERSON><PERSON> pengguna (UUID primary key)
2. **interviews** - Sesi interview dengan status dan tanggal
3. **questions** - Pertanyaan untuk setiap interview
4. **interview_responses** - Jawaban pengguna untuk setiap pertanyaan

### Relationships:
- users (1) → interviews (many)
- interviews (1) → questions (many)
- questions (1) → interview_responses (many)

## Setup dan Instalasi

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd interview-ai
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup Environment Variables**
   Buat file `.env` dengan konfigurasi Supabase:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Jalankan aplikasi**
   ```bash
   npm run dev
   ```

## Cara Penggunaan

1. **Registrasi/Login**: Buat akun baru atau login dengan akun yang sudah ada
2. **Buat Interview Baru**: Klik tombol "+" di dashboard untuk membuat sesi interview baru
3. **Mulai Interview**: Klik "Continue" pada card interview untuk memulai sesi
4. **Jawab Pertanyaan**: Ketik jawaban atau gunakan fitur perekaman suara
5. **Navigasi**: Gunakan tombol "Previous" dan "Next" untuk berpindah antar pertanyaan
6. **Selesaikan Interview**: Klik "Complete Interview" pada pertanyaan terakhir
7. **Lihat Hasil**: Akses hasil interview melalui tombol "View Results"

## Fitur Keamanan

- Row Level Security (RLS) diaktifkan pada semua tabel
- Pengguna hanya dapat mengakses data mereka sendiri
- Autentikasi JWT melalui Supabase Auth
- Trigger otomatis untuk membuat profil pengguna

## Development

### Scripts yang Tersedia:
- `npm run dev` - Menjalankan development server
- `npm run build` - Build aplikasi untuk production
- `npm run preview` - Preview build production
- `npm run lint` - Menjalankan ESLint

### Struktur Folder:
```
src/
├── components/
│   ├── Auth/           # Komponen autentikasi
│   ├── Dashboard/      # Dashboard utama
│   ├── Interview/      # Komponen interview
│   └── ProtectedRoute.jsx
├── contexts/
│   └── AuthContext.jsx # Context untuk autentikasi
├── hooks/
│   └── useInterviews.js # Custom hook untuk interview
├── lib/
│   └── supabase.js     # Konfigurasi Supabase client
└── App.jsx             # Komponen utama dengan routing
```
