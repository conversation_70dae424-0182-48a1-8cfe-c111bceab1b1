import mongoose from 'mongoose';

const keyBehaviorSchema = new mongoose.Schema({
  behavior: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  }
}, { _id: false });

const competencyLevelSchema = new mongoose.Schema({
  level: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  operationalDefinition: {
    type: String,
    required: true,
    trim: true
  },
  keyBehaviors: [keyBehaviorSchema]
}, { _id: false });

const competencySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Competency name is required'],
    trim: true,
    maxlength: [100, 'Competency name cannot exceed 100 characters']
  },
  code: {
    type: String,
    required: [true, 'Competency code is required'],
    unique: true,
    uppercase: true,
    trim: true,
    maxlength: [10, 'Competency code cannot exceed 10 characters']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: ['technical', 'behavioral', 'leadership', 'core'],
    default: 'behavioral'
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  operationalDefinition: {
    type: String,
    required: [true, 'Operational definition is required'],
    trim: true,
    maxlength: [1000, 'Operational definition cannot exceed 1000 characters']
  },
  levels: [competencyLevelSchema],
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  metadata: {
    source: {
      type: String,
      enum: ['manual', 'excel_upload', 'api'],
      default: 'manual'
    },
    version: {
      type: String,
      default: '1.0'
    },
    tags: [String]
  }
}, {
  timestamps: true
});

// Indexes for better query performance
competencySchema.index({ code: 1 });
competencySchema.index({ category: 1 });
competencySchema.index({ isActive: 1 });
competencySchema.index({ name: 'text', description: 'text' });

// Validation for levels array
competencySchema.pre('save', function(next) {
  if (this.levels && this.levels.length > 0) {
    // Check if levels are sequential and start from 1
    const sortedLevels = this.levels.sort((a, b) => a.level - b.level);
    for (let i = 0; i < sortedLevels.length; i++) {
      if (sortedLevels[i].level !== i + 1) {
        return next(new Error('Competency levels must be sequential starting from 1'));
      }
    }
  }
  next();
});

// Method to get level by number
competencySchema.methods.getLevelByNumber = function(levelNumber) {
  return this.levels.find(level => level.level === levelNumber);
};

// Method to get all behaviors for a specific level
competencySchema.methods.getBehaviorsForLevel = function(levelNumber) {
  const level = this.getLevelByNumber(levelNumber);
  return level ? level.keyBehaviors : [];
};

// Static method to find competencies by category
competencySchema.statics.findByCategory = function(category) {
  return this.find({ category, isActive: true }).sort({ name: 1 });
};

// Static method to search competencies
competencySchema.statics.searchCompetencies = function(searchTerm) {
  return this.find({
    $and: [
      { isActive: true },
      {
        $or: [
          { name: { $regex: searchTerm, $options: 'i' } },
          { description: { $regex: searchTerm, $options: 'i' } },
          { code: { $regex: searchTerm, $options: 'i' } }
        ]
      }
    ]
  }).sort({ name: 1 });
};

const Competency = mongoose.model('Competency', competencySchema);

export default Competency;
