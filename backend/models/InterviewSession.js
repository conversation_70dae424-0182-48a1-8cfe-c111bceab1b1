import mongoose from 'mongoose';

const questionSchema = new mongoose.Schema({
  questionId: {
    type: String,
    required: true
  },
  question: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['potential', 'competency', 'general', 'behavioral', 'technical'],
    required: true
  },
  competencyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Competency'
  },
  expectedTopics: [String],
  order: {
    type: Number,
    required: true
  }
}, { _id: false });

const answerSchema = new mongoose.Schema({
  questionId: {
    type: String,
    required: true
  },
  answer: {
    type: String,
    required: true,
    trim: true
  },
  transcript: {
    type: String,
    trim: true
  },
  duration: {
    type: Number, // in seconds
    min: 0
  },
  confidence: {
    type: Number,
    min: 0,
    max: 1
  },
  aiAnalysis: {
    relevance: {
      type: Number,
      min: 0,
      max: 5
    },
    clarity: {
      type: Number,
      min: 0,
      max: 5
    },
    completeness: {
      type: Number,
      min: 0,
      max: 5
    },
    keyPoints: [String],
    suggestions: [String]
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

const interviewSessionSchema = new mongoose.Schema({
  participantId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sessionId: {
    type: String,
    required: true,
    unique: true
  },
  type: {
    type: String,
    enum: ['potential', 'competency', 'combined'],
    required: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'in_progress', 'completed', 'cancelled', 'expired'],
    default: 'scheduled'
  },
  scheduledAt: {
    type: Date,
    required: true
  },
  startedAt: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  duration: {
    type: Number // in minutes
  },
  questions: [questionSchema],
  answers: [answerSchema],
  psikotesAssessmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PsikotesAssessment'
  },
  competencyIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Competency'
  }],
  settings: {
    questionsPerCompetency: {
      type: Number,
      default: 3,
      min: 1,
      max: 10
    },
    totalQuestions: {
      type: Number,
      default: 5,
      min: 1,
      max: 20
    },
    timeLimit: {
      type: Number, // in minutes
      default: 60
    },
    language: {
      type: String,
      default: 'id',
      enum: ['id', 'en']
    }
  },
  results: {
    overallScore: {
      type: Number,
      min: 0,
      max: 100
    },
    competencyScores: [{
      competencyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Competency'
      },
      score: {
        type: Number,
        min: 0,
        max: 5
      },
      level: {
        type: Number,
        min: 1,
        max: 5
      },
      feedback: String
    }],
    potentialScores: [{
      aspect: String,
      score: {
        type: Number,
        min: 0,
        max: 5
      },
      feedback: String
    }],
    recommendations: [String],
    strengths: [String],
    improvementAreas: [String]
  },
  feedback: {
    participantFeedback: {
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      comments: String,
      submittedAt: Date
    },
    adminNotes: String
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Indexes for better query performance
interviewSessionSchema.index({ participantId: 1 });
interviewSessionSchema.index({ sessionId: 1 });
interviewSessionSchema.index({ status: 1 });
interviewSessionSchema.index({ scheduledAt: 1 });
interviewSessionSchema.index({ type: 1 });

// Virtual for session progress
interviewSessionSchema.virtual('progress').get(function() {
  if (this.questions.length === 0) return 0;
  return Math.round((this.answers.length / this.questions.length) * 100);
});

// Method to check if session is expired
interviewSessionSchema.methods.isExpired = function() {
  if (this.status === 'completed' || this.status === 'cancelled') return false;
  
  const expiryTime = new Date(this.scheduledAt);
  expiryTime.setHours(expiryTime.getHours() + (this.settings.timeLimit / 60) + 24); // Add buffer time
  
  return Date.now() > expiryTime.getTime();
};

// Method to start session
interviewSessionSchema.methods.startSession = function() {
  this.status = 'in_progress';
  this.startedAt = new Date();
  return this.save();
};

// Method to complete session
interviewSessionSchema.methods.completeSession = function() {
  this.status = 'completed';
  this.completedAt = new Date();
  if (this.startedAt) {
    this.duration = Math.round((this.completedAt - this.startedAt) / (1000 * 60));
  }
  return this.save();
};

// Static method to find active sessions for participant
interviewSessionSchema.statics.findActiveByParticipant = function(participantId) {
  return this.find({
    participantId,
    status: { $in: ['scheduled', 'in_progress'] }
  }).sort({ scheduledAt: 1 });
};

const InterviewSession = mongoose.model('InterviewSession', interviewSessionSchema);

export default InterviewSession;
