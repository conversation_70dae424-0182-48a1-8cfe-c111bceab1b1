import mongoose from 'mongoose';

const psikogramAspectSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  score: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  description: {
    type: String,
    trim: true
  }
}, { _id: false });

const psikotesAssessmentSchema = new mongoose.Schema({
  participantId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  externalAssessmentId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  psikogramAspects: [psikogramAspectSchema],
  strengths: {
    type: String,
    required: true,
    trim: true
  },
  developmentAreas: {
    type: String,
    required: true,
    trim: true
  },
  developmentSuggestions: {
    type: String,
    required: true,
    trim: true
  },
  overallScore: {
    type: Number,
    min: 1,
    max: 5
  },
  assessmentDate: {
    type: Date,
    required: true
  },
  assessmentType: {
    type: String,
    enum: ['personality', 'cognitive', 'comprehensive'],
    default: 'personality'
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'expired'],
    default: 'completed'
  },
  metadata: {
    source: {
      type: String,
      default: 'external_api'
    },
    version: {
      type: String,
      default: '1.0'
    },
    processingTime: {
      type: Number // in milliseconds
    }
  }
}, {
  timestamps: true
});

// Indexes for better query performance
psikotesAssessmentSchema.index({ participantId: 1 });
psikotesAssessmentSchema.index({ externalAssessmentId: 1 });
psikotesAssessmentSchema.index({ assessmentDate: -1 });
psikotesAssessmentSchema.index({ status: 1 });

// Virtual for getting assessment age
psikotesAssessmentSchema.virtual('assessmentAge').get(function() {
  return Math.floor((Date.now() - this.assessmentDate.getTime()) / (1000 * 60 * 60 * 24));
});

// Method to check if assessment is recent (within 30 days)
psikotesAssessmentSchema.methods.isRecent = function() {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  return this.assessmentDate >= thirtyDaysAgo;
};

// Method to get formatted psikogram summary
psikotesAssessmentSchema.methods.getPsikogramSummary = function() {
  return this.psikogramAspects.map(aspect => ({
    name: aspect.name,
    score: aspect.score,
    level: this.getScoreLevel(aspect.score),
    description: aspect.description
  }));
};

// Method to convert score to level description
psikotesAssessmentSchema.methods.getScoreLevel = function(score) {
  const levels = {
    1: 'Buruk',
    2: 'Kurang',
    3: 'Cukup',
    4: 'Baik',
    5: 'Sangat Baik'
  };
  return levels[score] || 'Unknown';
};

// Static method to find recent assessment for participant
psikotesAssessmentSchema.statics.findRecentByParticipant = function(participantId) {
  return this.findOne({ 
    participantId, 
    status: 'completed' 
  }).sort({ assessmentDate: -1 });
};

const PsikotesAssessment = mongoose.model('PsikotesAssessment', psikotesAssessmentSchema);

export default PsikotesAssessment;
