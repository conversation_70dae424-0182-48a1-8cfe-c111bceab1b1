{"name": "interview-ai-backend", "version": "1.0.0", "description": "Backend API for Interview AI System", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedDatabase.js"}, "keywords": ["interview", "ai", "assessment", "api"], "author": "Interview AI Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.8.7", "morgan": "^1.10.0", "multer": "^1.4.4"}, "devDependencies": {"compression": "^1.8.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "jest": "^29.7.0", "joi": "^17.13.3", "nodemon": "^3.1.10", "supertest": "^6.3.3", "xlsx": "^0.18.5"}}