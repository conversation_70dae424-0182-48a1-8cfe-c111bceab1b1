import express from 'express';
import {
  register,
  login,
  logout,
  getMe,
  updateProfile,
  changePassword,
  refreshToken
} from '../controllers/authController.js';
import { protect } from '../middleware/auth.js';
import { validate, userValidation } from '../utils/validation.js';

const router = express.Router();

// Public routes
router.post('/register', validate(userValidation.register), register);
router.post('/login', validate(userValidation.login), login);
router.post('/refresh', refreshToken);

// Protected routes
router.use(protect); // All routes after this middleware are protected

router.post('/logout', logout);
router.get('/me', getMe);
router.put('/profile', validate(userValidation.updateProfile), updateProfile);
router.put('/change-password', validate(userValidation.changePassword), changePassword);

export default router;
