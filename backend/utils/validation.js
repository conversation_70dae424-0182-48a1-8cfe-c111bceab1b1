import Joi from 'joi';

// User validation schemas
export const userValidation = {
  register: Joi.object({
    name: Joi.string().min(2).max(100).required().trim(),
    email: Joi.string().email().required().lowercase().trim(),
    password: Joi.string().min(6).max(128).required(),
    role: Joi.string().valid('participant', 'admin').default('participant'),
    profile: Joi.object({
      phone: Joi.string().trim(),
      position: Joi.string().trim(),
      department: Joi.string().trim(),
      experience: Joi.number().min(0)
    })
  }),

  login: Joi.object({
    email: Joi.string().email().required().lowercase().trim(),
    password: Joi.string().required()
  }),

  updateProfile: Joi.object({
    name: Joi.string().min(2).max(100).trim(),
    profile: Joi.object({
      phone: Joi.string().trim(),
      position: Joi.string().trim(),
      department: Joi.string().trim(),
      experience: Joi.number().min(0)
    })
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(6).max(128).required()
  })
};

// Psikotes assessment validation
export const psikotesValidation = {
  create: Joi.object({
    participantId: Joi.string().required(),
    externalAssessmentId: Joi.string().required().trim(),
    psikogramAspects: Joi.array().items(
      Joi.object({
        name: Joi.string().required().trim(),
        score: Joi.number().min(1).max(5).required(),
        description: Joi.string().trim()
      })
    ).required(),
    strengths: Joi.string().required().trim(),
    developmentAreas: Joi.string().required().trim(),
    developmentSuggestions: Joi.string().required().trim(),
    overallScore: Joi.number().min(1).max(5),
    assessmentDate: Joi.date().required(),
    assessmentType: Joi.string().valid('personality', 'cognitive', 'comprehensive').default('personality')
  })
};

// Competency validation
export const competencyValidation = {
  create: Joi.object({
    name: Joi.string().min(2).max(100).required().trim(),
    code: Joi.string().min(2).max(10).required().uppercase().trim(),
    category: Joi.string().valid('technical', 'behavioral', 'leadership', 'core').required(),
    description: Joi.string().max(500).required().trim(),
    operationalDefinition: Joi.string().max(1000).required().trim(),
    levels: Joi.array().items(
      Joi.object({
        level: Joi.number().min(1).max(5).required(),
        name: Joi.string().required().trim(),
        operationalDefinition: Joi.string().required().trim(),
        keyBehaviors: Joi.array().items(
          Joi.object({
            behavior: Joi.string().required().trim(),
            description: Joi.string().trim()
          })
        )
      })
    ).required(),
    metadata: Joi.object({
      tags: Joi.array().items(Joi.string().trim())
    })
  }),

  update: Joi.object({
    name: Joi.string().min(2).max(100).trim(),
    description: Joi.string().max(500).trim(),
    operationalDefinition: Joi.string().max(1000).trim(),
    levels: Joi.array().items(
      Joi.object({
        level: Joi.number().min(1).max(5).required(),
        name: Joi.string().required().trim(),
        operationalDefinition: Joi.string().required().trim(),
        keyBehaviors: Joi.array().items(
          Joi.object({
            behavior: Joi.string().required().trim(),
            description: Joi.string().trim()
          })
        )
      })
    ),
    isActive: Joi.boolean(),
    metadata: Joi.object({
      tags: Joi.array().items(Joi.string().trim())
    })
  })
};

// Interview session validation
export const interviewValidation = {
  create: Joi.object({
    participantId: Joi.string().required(),
    type: Joi.string().valid('potential', 'competency', 'combined').required(),
    scheduledAt: Joi.date().min('now').required(),
    competencyIds: Joi.array().items(Joi.string()).when('type', {
      is: Joi.string().valid('competency', 'combined'),
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    settings: Joi.object({
      questionsPerCompetency: Joi.number().min(1).max(10).default(3),
      totalQuestions: Joi.number().min(1).max(20).default(5),
      timeLimit: Joi.number().min(15).max(180).default(60),
      language: Joi.string().valid('id', 'en').default('id')
    })
  }),

  submitAnswer: Joi.object({
    questionId: Joi.string().required(),
    answer: Joi.string().required().trim(),
    transcript: Joi.string().trim(),
    duration: Joi.number().min(0),
    confidence: Joi.number().min(0).max(1)
  }),

  feedback: Joi.object({
    rating: Joi.number().min(1).max(5).required(),
    comments: Joi.string().trim()
  })
};

// Validation middleware
export const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }

    req.body = value;
    next();
  };
};
