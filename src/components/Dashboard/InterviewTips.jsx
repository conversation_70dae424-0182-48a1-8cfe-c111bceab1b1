import { 
  Card, 
  CardContent, 
  Typography, 
  List, 
  ListItem, 
  ListItemIcon,
  ListItemText,
  Box
} from '@mui/material'
import { 
  Lightbulb as LightbulbIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material'

const InterviewTips = () => {
  const tips = [
    "Practice the STAR method (Situation, Task, Action, Result) for behavioral questions",
    "Research the company and role thoroughly before the interview",
    "Prepare specific examples that demonstrate your skills and achievements",
    "Practice speaking clearly and at an appropriate pace",
    "Prepare thoughtful questions to ask the interviewer",
    "Use the voice recording feature to practice your delivery",
    "Review your responses and refine them for clarity and impact"
  ]

  return (
    <Card sx={{ mt: 3 }}>
      <CardContent>
        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <LightbulbIcon color="primary" />
          <Typography variant="h6">
            Interview Tips
          </Typography>
        </Box>
        
        <List dense>
          {tips.map((tip, index) => (
            <ListItem key={index} sx={{ pl: 0 }}>
              <ListItemIcon sx={{ minWidth: 32 }}>
                <CheckIcon color="success" fontSize="small" />
              </ListItemIcon>
              <ListItemText 
                primary={tip}
                primaryTypographyProps={{ variant: 'body2' }}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  )
}

export default InterviewTips
