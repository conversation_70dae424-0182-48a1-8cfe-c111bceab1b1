import { Box, Card, CardContent, Typography, Grid } from '@mui/material'
import { 
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material'

const InterviewStats = ({ interviews }) => {
  const totalInterviews = interviews.length
  const completedInterviews = interviews.filter(i => i.status === 'completed').length
  const inProgressInterviews = interviews.filter(i => i.status === 'in_progress').length
  const completionRate = totalInterviews > 0 ? Math.round((completedInterviews / totalInterviews) * 100) : 0

  const stats = [
    {
      title: 'Total Interviews',
      value: totalInterviews,
      icon: <AssignmentIcon />,
      color: 'primary.main'
    },
    {
      title: 'Completed',
      value: completedInterviews,
      icon: <CheckCircleIcon />,
      color: 'success.main'
    },
    {
      title: 'In Progress',
      value: inProgressInterviews,
      icon: <ScheduleIcon />,
      color: 'warning.main'
    },
    {
      title: 'Completion Rate',
      value: `${completionRate}%`,
      icon: <TrendingUpIcon />,
      color: 'info.main'
    }
  ]

  return (
    <Grid container spacing={3} sx={{ mb: 4 }}>
      {stats.map((stat, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" component="div" gutterBottom>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stat.title}
                  </Typography>
                </Box>
                <Box sx={{ color: stat.color }}>
                  {stat.icon}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  )
}

export default InterviewStats
