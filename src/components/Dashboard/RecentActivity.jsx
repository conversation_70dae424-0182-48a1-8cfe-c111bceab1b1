import { 
  Card, 
  CardContent, 
  Typography, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemIcon,
  Chip,
  Box
} from '@mui/material'
import { 
  PlayArrow as PlayIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material'

const RecentActivity = ({ interviews }) => {
  // Sort interviews by date and take the 5 most recent
  const recentInterviews = interviews
    .sort((a, b) => new Date(b.interview_date) - new Date(a.interview_date))
    .slice(0, 5)

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now - date)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return 'Today'
    if (diffDays === 2) return 'Yesterday'
    if (diffDays <= 7) return `${diffDays - 1} days ago`
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckIcon color="success" />
      case 'in_progress':
        return <ScheduleIcon color="warning" />
      default:
        return <PlayIcon color="primary" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'in_progress':
        return 'warning'
      default:
        return 'primary'
    }
  }

  if (recentInterviews.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Activity
          </Typography>
          <Typography variant="body2" color="text.secondary">
            No recent interview activity
          </Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Recent Activity
        </Typography>
        <List>
          {recentInterviews.map((interview, index) => (
            <ListItem key={interview.id} divider={index < recentInterviews.length - 1}>
              <ListItemIcon>
                {getStatusIcon(interview.status)}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="body1">
                      Interview #{interview.id}
                    </Typography>
                    <Chip 
                      label={interview.status} 
                      size="small" 
                      color={getStatusColor(interview.status)}
                    />
                  </Box>
                }
                secondary={formatDate(interview.interview_date)}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  )
}

export default RecentActivity
