import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { supabase } from '../../lib/supabase'
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Fab,
  Alert
} from '@mui/material'
import { Add as AddIcon, PlayArrow as PlayIcon } from '@mui/icons-material'

const Dashboard = () => {
  const { user, signOut } = useAuth()
  const navigate = useNavigate()
  const [interviews, setInterviews] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchInterviews()
  }, [user])

  const fetchInterviews = async () => {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          *,
          questions (
            id,
            question_text,
            interview_responses (
              id,
              user_response
            )
          )
        `)
        .eq('user_id', user.id)
        .order('interview_date', { ascending: false })

      if (error) throw error
      setInterviews(data || [])
    } catch (error) {
      setError('Failed to fetch interviews: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleSignOut = async () => {
    await signOut()
  }

  const createNewInterview = async () => {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .insert([
          {
            user_id: user.id,
            interview_date: new Date().toISOString(),
            status: 'in_progress'
          }
        ])
        .select()
        .single()

      if (error) throw error
      
      // Add some sample questions
      const sampleQuestions = [
        "Tell me about yourself and your background.",
        "What are your greatest strengths?",
        "Describe a challenging situation you faced and how you handled it.",
        "Where do you see yourself in 5 years?",
        "Why are you interested in this position?"
      ]

      const questionsToInsert = sampleQuestions.map(questionText => ({
        interview_id: data.id,
        question_text: questionText
      }))

      await supabase
        .from('questions')
        .insert(questionsToInsert)

      fetchInterviews()
    } catch (error) {
      setError('Failed to create interview: ' + error.message)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Box>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Interview AI Dashboard
          </Typography>
          <Typography variant="body2" sx={{ mr: 2 }}>
            Welcome, {user?.user_metadata?.username || user?.email}
          </Typography>
          <Button color="inherit" onClick={handleSignOut}>
            Sign Out
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Typography variant="h4" gutterBottom>
          Your Interviews
        </Typography>

        {loading ? (
          <Typography>Loading interviews...</Typography>
        ) : interviews.length === 0 ? (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                No interviews yet
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Create your first interview to get started with AI-powered practice sessions.
              </Typography>
            </CardContent>
          </Card>
        ) : (
          <Grid container spacing={3}>
            {interviews.map((interview) => (
              <Grid item xs={12} md={6} lg={4} key={interview.id}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Interview #{interview.id}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {formatDate(interview.interview_date)}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      Status: <strong>{interview.status}</strong>
                    </Typography>
                    <Typography variant="body2">
                      Questions: {interview.questions?.length || 0}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<PlayIcon />}
                      onClick={() => {
                        if (interview.status === 'completed') {
                          navigate(`/interview/${interview.id}/results`)
                        } else {
                          navigate(`/interview/${interview.id}`)
                        }
                      }}
                    >
                      {interview.status === 'completed' ? 'View Results' : 'Continue'}
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        <Fab
          color="primary"
          aria-label="add"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={createNewInterview}
        >
          <AddIcon />
        </Fab>
      </Container>
    </Box>
  )
}

export default Dashboard
