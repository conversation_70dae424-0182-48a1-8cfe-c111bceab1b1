import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { supabase } from '../../lib/supabase'
import InterviewStats from './InterviewStats'
import RecentActivity from './RecentActivity'
import InterviewTips from './InterviewTips'
import LoadingSpinner from '../common/LoadingSpinner'
import EditInterviewDialog from '../Interview/EditInterviewDialog'
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Fab,
  Alert,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText
} from '@mui/material'
import {
  Add as AddIcon,
  PlayArrow as PlayIcon,
  MoreVert as MoreVertIcon,
  Delete as DeleteIcon,
  Edit as EditIcon
} from '@mui/icons-material'

const Dashboard = () => {
  const { user, signOut } = useAuth()
  const navigate = useNavigate()
  const [interviews, setInterviews] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [anchorEl, setAnchorEl] = useState(null)
  const [selectedInterview, setSelectedInterview] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  useEffect(() => {
    fetchInterviews()
  }, [user])

  const fetchInterviews = async () => {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          *,
          questions (
            id,
            question_text,
            interview_responses (
              id,
              user_response
            )
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setInterviews(data || [])
    } catch (error) {
      setError('Failed to fetch interviews: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleSignOut = async () => {
    await signOut()
  }

  const createNewInterview = async () => {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .insert([
          {
            user_id: user.id,
            interview_date: new Date().toISOString(),
            status: 'in_progress'
          }
        ])
        .select()
        .single()

      if (error) throw error

      // Add some sample questions
      const sampleQuestions = [
        "Tell me about yourself and your background.",
        "What are your greatest strengths?",
        "Describe a challenging situation you faced and how you handled it.",
        "Where do you see yourself in 5 years?",
        "Why are you interested in this position?"
      ]

      const questionsToInsert = sampleQuestions.map(questionText => ({
        interview_id: data.id,
        question_text: questionText
      }))

      await supabase
        .from('questions')
        .insert(questionsToInsert)

      fetchInterviews()
    } catch (error) {
      setError('Failed to create interview: ' + error.message)
    }
  }

  const handleMenuOpen = (event, interview) => {
    setAnchorEl(event.currentTarget)
    setSelectedInterview(interview)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedInterview(null)
  }

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true)
    handleMenuClose()
  }

  const handleEditClick = () => {
    setEditDialogOpen(true)
    handleMenuClose()
  }

  const handleDeleteConfirm = async () => {
    try {
      const { error } = await supabase
        .from('interviews')
        .delete()
        .eq('id', selectedInterview.id)

      if (error) throw error

      setDeleteDialogOpen(false)
      setSelectedInterview(null)
      fetchInterviews()
    } catch (error) {
      setError('Failed to delete interview: ' + error.message)
    }
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setSelectedInterview(null)
  }

  const handleEditDialogClose = () => {
    setEditDialogOpen(false)
    setSelectedInterview(null)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Box>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Interview AI Dashboard
          </Typography>
          <Typography variant="body2" sx={{ mr: 2 }}>
            Welcome, {user?.user_metadata?.username || user?.email}
          </Typography>
          <Button color="inherit" onClick={handleSignOut}>
            Sign Out
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <InterviewStats interviews={interviews} />

        <Grid container spacing={3}>
          <Grid item xs={12} lg={8}>
            <Typography variant="h4" gutterBottom>
              Your Interviews
            </Typography>

            {loading ? (
              <LoadingSpinner message="Loading interviews..." />
            ) : interviews.length === 0 ? (
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    No interviews yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Create your first interview to get started with AI-powered practice sessions.
                  </Typography>
                </CardContent>
              </Card>
            ) : (
              <Grid container spacing={3}>
                {interviews.map((interview) => (
              <Grid item xs={12} md={6} key={interview.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                      <Box>
                        <Typography variant="h6" gutterBottom>
                          {interview.title || `Interview #${interview.id}`}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {formatDate(interview.interview_date)}
                        </Typography>
                        <Typography variant="body2" gutterBottom>
                          Status: <strong>{interview.status}</strong>
                        </Typography>
                        <Typography variant="body2">
                          Questions: {interview.questions?.length || 0}
                        </Typography>
                      </Box>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, interview)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<PlayIcon />}
                      onClick={() => {
                        if (interview.status === 'completed') {
                          navigate(`/interview/${interview.id}/results`)
                        } else {
                          navigate(`/interview/${interview.id}`)
                        }
                      }}
                    >
                      {interview.status === 'completed' ? 'View Results' : 'Continue'}
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
              </Grid>
            )}
          </Grid>

          <Grid item xs={12} lg={4}>
            <RecentActivity interviews={interviews} />
            <InterviewTips />
          </Grid>
        </Grid>

        <Fab
          color="primary"
          aria-label="add"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={createNewInterview}
        >
          <AddIcon />
        </Fab>

        {/* Menu for interview options */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleEditClick}>
            <EditIcon sx={{ mr: 1 }} />
            Edit Interview
          </MenuItem>
          <MenuItem onClick={handleDeleteClick}>
            <DeleteIcon sx={{ mr: 1 }} />
            Delete Interview
          </MenuItem>
        </Menu>

        {/* Delete confirmation dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={handleDeleteCancel}
        >
          <DialogTitle>Delete Interview</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete Interview #{selectedInterview?.id}?
              This action cannot be undone and will delete all questions and responses.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDeleteCancel}>Cancel</Button>
            <Button onClick={handleDeleteConfirm} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Edit Interview Dialog */}
        <EditInterviewDialog
          open={editDialogOpen}
          onClose={handleEditDialogClose}
          interview={selectedInterview}
          onUpdate={fetchInterviews}
        />
      </Container>
    </Box>
  )
}

export default Dashboard
