import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  LinearProgress,
  Alert,
  AppBar,
  Toolbar,
  IconButton
} from '@mui/material'
import { ArrowBack, Mic, MicOff } from '@mui/icons-material'
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition'

const InterviewSession = () => {
  const { interviewId } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  
  const [interview, setInterview] = useState(null)
  const [questions, setQuestions] = useState([])
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [responses, setResponses] = useState({})
  const [currentResponse, setCurrentResponse] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [saving, setSaving] = useState(false)

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition()

  useEffect(() => {
    fetchInterviewData()
  }, [interviewId])

  useEffect(() => {
    if (transcript) {
      setCurrentResponse(prev => prev + ' ' + transcript)
    }
  }, [transcript])

  const fetchInterviewData = async () => {
    try {
      const { data: interviewData, error: interviewError } = await supabase
        .from('interviews')
        .select('*')
        .eq('id', interviewId)
        .eq('user_id', user.id)
        .single()

      if (interviewError) throw interviewError

      const { data: questionsData, error: questionsError } = await supabase
        .from('questions')
        .select(`
          *,
          interview_responses (
            id,
            user_response
          )
        `)
        .eq('interview_id', interviewId)
        .order('id')

      if (questionsError) throw questionsError

      setInterview(interviewData)
      setQuestions(questionsData)

      // Load existing responses
      const existingResponses = {}
      questionsData.forEach(question => {
        if (question.interview_responses.length > 0) {
          existingResponses[question.id] = question.interview_responses[0].user_response
        }
      })
      setResponses(existingResponses)

      // Set current response if exists
      if (questionsData.length > 0 && existingResponses[questionsData[0].id]) {
        setCurrentResponse(existingResponses[questionsData[0].id])
      }

    } catch (err) {
      setError('Failed to load interview: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const saveResponse = async (questionId, response) => {
    if (!response.trim()) return

    setSaving(true)
    try {
      const { error } = await supabase
        .from('interview_responses')
        .upsert([
          {
            question_id: questionId,
            user_response: response.trim()
          }
        ])

      if (error) throw error

      setResponses(prev => ({
        ...prev,
        [questionId]: response.trim()
      }))

    } catch (err) {
      setError('Failed to save response: ' + err.message)
    } finally {
      setSaving(false)
    }
  }

  const handleNextQuestion = async () => {
    const currentQuestion = questions[currentQuestionIndex]
    if (currentResponse.trim()) {
      await saveResponse(currentQuestion.id, currentResponse)
    }

    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
      const nextQuestion = questions[currentQuestionIndex + 1]
      setCurrentResponse(responses[nextQuestion.id] || '')
    } else {
      // Interview completed
      await completeInterview()
    }
  }

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      const currentQuestion = questions[currentQuestionIndex]
      if (currentResponse.trim()) {
        saveResponse(currentQuestion.id, currentResponse)
      }
      
      setCurrentQuestionIndex(prev => prev - 1)
      const prevQuestion = questions[currentQuestionIndex - 1]
      setCurrentResponse(responses[prevQuestion.id] || '')
    }
  }

  const completeInterview = async () => {
    try {
      const { error } = await supabase
        .from('interviews')
        .update({ status: 'completed' })
        .eq('id', interviewId)

      if (error) throw error
      navigate(`/interview/${interviewId}/results`)
    } catch (err) {
      setError('Failed to complete interview: ' + err.message)
    }
  }

  const toggleListening = () => {
    if (listening) {
      SpeechRecognition.stopListening()
    } else {
      resetTranscript()
      SpeechRecognition.startListening({ continuous: true })
    }
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Loading interview...</Typography>
      </Box>
    )
  }

  if (!interview || questions.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Interview not found</Typography>
      </Box>
    )
  }

  const currentQuestion = questions[currentQuestionIndex]
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100

  return (
    <Box>
      <AppBar position="static">
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => navigate('/dashboard')}
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            Interview Session
          </Typography>
          <Typography variant="body2">
            Question {currentQuestionIndex + 1} of {questions.length}
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{ p: 3 }}>
        <LinearProgress variant="determinate" value={progress} sx={{ mb: 3 }} />

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              {currentQuestion.question_text}
            </Typography>
            
            <TextField
              fullWidth
              multiline
              rows={6}
              value={currentResponse}
              onChange={(e) => setCurrentResponse(e.target.value)}
              placeholder="Type your answer here or use voice recording..."
              sx={{ mt: 2 }}
            />

            <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
              {browserSupportsSpeechRecognition ? (
                <>
                  <Button
                    variant={listening ? "contained" : "outlined"}
                    startIcon={listening ? <MicOff /> : <Mic />}
                    onClick={toggleListening}
                    color={listening ? "secondary" : "primary"}
                  >
                    {listening ? 'Stop Recording' : 'Start Recording'}
                  </Button>
                  {listening && (
                    <Typography variant="body2" color="text.secondary">
                      Listening...
                    </Typography>
                  )}
                </>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Speech recognition not supported in this browser
                </Typography>
              )}
            </Box>
          </CardContent>
        </Card>

        <Box display="flex" justifyContent="space-between">
          <Button
            variant="outlined"
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
          >
            Previous
          </Button>
          
          <Button
            variant="contained"
            onClick={handleNextQuestion}
            disabled={saving}
          >
            {saving ? 'Saving...' : 
             currentQuestionIndex === questions.length - 1 ? 'Complete Interview' : 'Next Question'}
          </Button>
        </Box>
      </Box>
    </Box>
  )
}

export default InterviewSession
