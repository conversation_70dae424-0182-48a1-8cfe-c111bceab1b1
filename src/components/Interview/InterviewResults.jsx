import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'
import { exportInterviewToPDF, exportInterviewToJSON } from '../../utils/exportUtils'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Menu,
  MenuItem
} from '@mui/material'
import {
  ArrowBack,
  ExpandMore,
  Download as DownloadIcon,
  GetApp as GetAppIcon
} from '@mui/icons-material'

const InterviewResults = () => {
  const { interviewId } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  
  const [interview, setInterview] = useState(null)
  const [questionsWithResponses, setQuestionsWithResponses] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [exportMenuAnchor, setExportMenuAnchor] = useState(null)

  useEffect(() => {
    fetchInterviewResults()
  }, [interviewId])

  const fetchInterviewResults = async () => {
    try {
      const { data: interviewData, error: interviewError } = await supabase
        .from('interviews')
        .select('*')
        .eq('id', interviewId)
        .eq('user_id', user.id)
        .single()

      if (interviewError) throw interviewError

      const { data: questionsData, error: questionsError } = await supabase
        .from('questions')
        .select(`
          *,
          interview_responses (
            id,
            user_response
          )
        `)
        .eq('interview_id', interviewId)
        .order('id')

      if (questionsError) throw questionsError

      setInterview(interviewData)
      setQuestionsWithResponses(questionsData)

    } catch (err) {
      setError('Failed to load interview results: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getResponseCount = () => {
    return questionsWithResponses.filter(q =>
      q.interview_responses.length > 0 && q.interview_responses[0].user_response.trim()
    ).length
  }

  const handleExportMenuOpen = (event) => {
    setExportMenuAnchor(event.currentTarget)
  }

  const handleExportMenuClose = () => {
    setExportMenuAnchor(null)
  }

  const handleExportTXT = () => {
    exportInterviewToPDF(interview, questionsWithResponses)
    handleExportMenuClose()
  }

  const handleExportJSON = () => {
    exportInterviewToJSON(interview, questionsWithResponses)
    handleExportMenuClose()
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Loading interview results...</Typography>
      </Box>
    )
  }

  if (!interview) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Interview not found</Typography>
      </Box>
    )
  }

  return (
    <Box>
      <AppBar position="static">
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => navigate('/dashboard')}
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            Interview Results
          </Typography>
          <IconButton
            color="inherit"
            onClick={handleExportMenuOpen}
          >
            <DownloadIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Box sx={{ p: 3 }}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h4">
                Interview #{interview.id}
              </Typography>
              <Chip 
                label={interview.status} 
                color={interview.status === 'completed' ? 'success' : 'primary'}
              />
            </Box>
            
            <Typography variant="body1" color="text.secondary" gutterBottom>
              Date: {formatDate(interview.interview_date)}
            </Typography>
            
            <Typography variant="body1" color="text.secondary">
              Responses: {getResponseCount()} of {questionsWithResponses.length} questions answered
            </Typography>
          </CardContent>
        </Card>

        <Typography variant="h5" gutterBottom>
          Questions & Responses
        </Typography>

        {questionsWithResponses.map((question, index) => {
          const response = question.interview_responses[0]
          const hasResponse = response && response.user_response.trim()

          return (
            <Accordion key={question.id} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Box display="flex" alignItems="center" width="100%">
                  <Typography variant="h6" sx={{ flexGrow: 1 }}>
                    Question {index + 1}
                  </Typography>
                  <Chip 
                    size="small"
                    label={hasResponse ? 'Answered' : 'No Response'}
                    color={hasResponse ? 'success' : 'default'}
                    sx={{ mr: 2 }}
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                    Question:
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {question.question_text}
                  </Typography>
                  
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                    Your Response:
                  </Typography>
                  <Typography variant="body1" sx={{ 
                    fontStyle: hasResponse ? 'normal' : 'italic',
                    color: hasResponse ? 'text.primary' : 'text.secondary'
                  }}>
                    {hasResponse ? response.user_response : 'No response provided'}
                  </Typography>
                </Box>
              </AccordionDetails>
            </Accordion>
          )
        })}

        <Box sx={{ mt: 4, display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            onClick={() => navigate('/dashboard')}
          >
            Back to Dashboard
          </Button>
          
          {interview.status !== 'completed' && (
            <Button
              variant="contained"
              onClick={() => navigate(`/interview/${interview.id}`)}
            >
              Continue Interview
            </Button>
          )}
        </Box>

        {/* Export Menu */}
        <Menu
          anchorEl={exportMenuAnchor}
          open={Boolean(exportMenuAnchor)}
          onClose={handleExportMenuClose}
        >
          <MenuItem onClick={handleExportTXT}>
            <GetAppIcon sx={{ mr: 1 }} />
            Export as Text
          </MenuItem>
          <MenuItem onClick={handleExportJSON}>
            <GetAppIcon sx={{ mr: 1 }} />
            Export as JSON
          </MenuItem>
        </Menu>
      </Box>
    </Box>
  )
}

export default InterviewResults
