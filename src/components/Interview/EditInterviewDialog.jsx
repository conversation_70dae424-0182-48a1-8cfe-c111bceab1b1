import { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box
} from '@mui/material'
import { supabase } from '../../lib/supabase'

const EditInterviewDialog = ({ open, onClose, interview, onUpdate }) => {
  const [title, setTitle] = useState(interview?.title || '')
  const [description, setDescription] = useState(interview?.description || '')
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')

  const handleSave = async () => {
    setSaving(true)
    setError('')

    try {
      const { error } = await supabase
        .from('interviews')
        .update({
          title: title.trim() || 'Interview Session',
          description: description.trim()
        })
        .eq('id', interview.id)

      if (error) throw error

      onUpdate()
      onClose()
    } catch (err) {
      setError('Failed to update interview: ' + err.message)
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    setTitle(interview?.title || '')
    setDescription(interview?.description || '')
    setError('')
    onClose()
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Edit Interview</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 1 }}>
          <TextField
            fullWidth
            label="Interview Title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            margin="normal"
            placeholder="Interview Session"
          />
          
          <TextField
            fullWidth
            label="Description (Optional)"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            margin="normal"
            multiline
            rows={3}
            placeholder="Add a description for this interview session..."
          />

          {error && (
            <Box sx={{ mt: 2, color: 'error.main' }}>
              {error}
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button 
          onClick={handleSave} 
          variant="contained"
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default EditInterviewDialog
