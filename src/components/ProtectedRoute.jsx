import { useAuth } from '../contexts/AuthContext'
import { Box, CircularProgress } from '@mui/material'
import AuthPage from './Auth/AuthPage'

const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    )
  }

  if (!user) {
    return <AuthPage />
  }

  return children
}

export default ProtectedRoute
