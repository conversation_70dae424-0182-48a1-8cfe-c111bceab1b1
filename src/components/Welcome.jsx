import { useState } from 'react';
import { 
  <PERSON>, 
  Typography, 
  Button, 
  Card, 
  CardContent, 
  TextField, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem,
  Stack
} from '@mui/material';

const Welcome = ({ onStart }) => {
  const [name, setName] = useState('');
  const [role, setRole] = useState('general');
  
  const handleStart = () => {
    onStart({ name, role });
  };
  
  return (
    <Box sx={{ maxWidth: 600, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom align="center">
        AI Voice Interview Simulator
      </Typography>
      
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Welcome to the Interview Simulator
          </Typography>
          
          <Typography paragraph>
            Practice your interview skills with our AI-powered voice interview simulator.
            The system will ask you questions and analyze your responses in real-time.
          </Typography>
          
          <Typography paragraph>
            Make sure your microphone is working properly and you're in a quiet environment.
          </Typography>
          
          <Stack spacing={3} sx={{ mt: 3 }}>
            <TextField
              label="Your Name"
              variant="outlined"
              fullWidth
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
            
            <FormControl fullWidth>
              <InputLabel>Interview Type</InputLabel>
              <Select
                value={role}
                label="Interview Type"
                onChange={(e) => setRole(e.target.value)}
              >
                <MenuItem value="general">General Interview</MenuItem>
                <MenuItem value="technical">Technical Interview</MenuItem>
                <MenuItem value="behavioral">Behavioral Interview</MenuItem>
                <MenuItem value="mixed">Mixed Questions</MenuItem>
              </Select>
            </FormControl>
            
            <Button 
              variant="contained" 
              color="primary" 
              size="large"
              onClick={handleStart}
              disabled={!name.trim()}
              fullWidth
            >
              Start Interview
            </Button>
          </Stack>
        </CardContent>
      </Card>
      
      <Typography variant="body2" color="text.secondary" align="center">
        This application uses your browser's Speech Recognition and Speech Synthesis APIs.
        No data is sent to external servers.
      </Typography>
    </Box>
  );
};

export default Welcome;
