import { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  CircularProgress, 
  Chip,
  Stack,
  Card,
  CardContent,
  IconButton
} from '@mui/material';
import MicIcon from '@mui/icons-material/Mic';
import MicOffIcon from '@mui/icons-material/MicOff';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import useSpeechRecognition from '../hooks/useSpeechRecognition';
import useSpeechSynthesis from '../hooks/useSpeechSynthesis';
import { getMixedQuestions } from '../data/interviewQuestions';

const Interview = () => {
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [interviewStarted, setInterviewStarted] = useState(false);
  const [interviewCompleted, setInterviewCompleted] = useState(false);
  const [isThinking, setIsThinking] = useState(false);
  
  const { 
    transcript, 
    isListening, 
    startListening, 
    stopListening, 
    resetTranscript,
    error: recognitionError 
  } = useSpeechRecognition();
  
  const { 
    speak, 
    cancel, 
    speaking, 
    error: synthesisError 
  } = useSpeechSynthesis();

  // Load questions
  useEffect(() => {
    const loadedQuestions = getMixedQuestions(5);
    setQuestions(loadedQuestions);
  }, []);

  // Current question
  const currentQuestion = questions[currentQuestionIndex];

  // Start the interview
  const startInterview = useCallback(() => {
    setInterviewStarted(true);
    setTimeout(() => {
      speak("Welcome to your AI interview. I'll ask you a series of questions. Please respond naturally when you see the microphone is active. Let's begin with the first question.");
      setTimeout(() => {
        if (currentQuestion) {
          speak(currentQuestion.question);
        }
      }, 1000);
    }, 500);
  }, [speak, currentQuestion]);

  // Handle answer submission
  const submitAnswer = useCallback(() => {
    if (!transcript.trim()) return;
    
    stopListening();
    setIsThinking(true);
    
    // Save the answer
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: transcript
    }));
    
    // Simulate AI thinking
    setTimeout(() => {
      setIsThinking(false);
      
      // Move to next question or end interview
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(prev => prev + 1);
        resetTranscript();
        
        // Ask the next question after a short delay
        setTimeout(() => {
          speak(questions[currentQuestionIndex + 1].question);
        }, 1000);
      } else {
        // End the interview
        setInterviewCompleted(true);
        speak("Thank you for completing the interview. You can now review your answers and get feedback.");
      }
    }, 2000);
  }, [currentQuestionIndex, questions, transcript, stopListening, resetTranscript, speak, currentQuestion]);

  // Listen for user's answer
  const listenForAnswer = useCallback(() => {
    resetTranscript();
    startListening();
  }, [resetTranscript, startListening]);

  // Repeat the current question
  const repeatQuestion = useCallback(() => {
    if (currentQuestion) {
      speak(currentQuestion.question);
    }
  }, [speak, currentQuestion]);

  // Restart the interview
  const restartInterview = useCallback(() => {
    setCurrentQuestionIndex(0);
    setAnswers({});
    setInterviewStarted(false);
    setInterviewCompleted(false);
    resetTranscript();
    cancel();
  }, [resetTranscript, cancel]);

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom align="center">
        AI Voice Interview
      </Typography>
      
      {!interviewStarted ? (
        <Card sx={{ mb: 4, p: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Welcome to your AI Interview
            </Typography>
            <Typography paragraph>
              This is a voice-based interview simulation. The AI interviewer will ask you questions,
              and you can respond using your voice. Make sure your microphone is working properly.
            </Typography>
            <Button 
              variant="contained" 
              color="primary" 
              onClick={startInterview}
              disabled={questions.length === 0}
              fullWidth
            >
              {questions.length === 0 ? <CircularProgress size={24} /> : 'Start Interview'}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          {!interviewCompleted ? (
            <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Question {currentQuestionIndex + 1} of {questions.length}
              </Typography>
              
              {currentQuestion && (
                <>
                  <Typography variant="h5" paragraph>
                    {currentQuestion.question}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <IconButton 
                      color="primary" 
                      onClick={repeatQuestion}
                      disabled={speaking || isThinking}
                    >
                      <VolumeUpIcon />
                    </IconButton>
                    <Typography variant="body2" color="text.secondary">
                      Click to hear the question again
                    </Typography>
                  </Box>
                  
                  <Box sx={{ my: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                    <Typography>
                      {transcript || 'Your answer will appear here...'}
                    </Typography>
                  </Box>
                  
                  <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
                    {isThinking ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <CircularProgress size={24} sx={{ mr: 2 }} />
                        <Typography>Processing your answer...</Typography>
                      </Box>
                    ) : (
                      <>
                        <Button
                          variant="contained"
                          color={isListening ? "error" : "primary"}
                          startIcon={isListening ? <MicOffIcon /> : <MicIcon />}
                          onClick={isListening ? stopListening : listenForAnswer}
                          disabled={speaking}
                        >
                          {isListening ? 'Stop Recording' : 'Start Recording'}
                        </Button>
                        
                        <Button
                          variant="contained"
                          color="success"
                          onClick={submitAnswer}
                          disabled={!transcript.trim() || isListening || speaking || isThinking}
                        >
                          Submit Answer
                        </Button>
                      </>
                    )}
                  </Stack>
                </>
              )}
            </Paper>
          ) : (
            <Paper elevation={3} sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom>
                Interview Completed
              </Typography>
              <Typography paragraph>
                Thank you for completing the interview. Here are your answers:
              </Typography>
              
              {questions.map((q, index) => (
                <Box key={q.id} sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    Question {index + 1}: {q.question}
                  </Typography>
                  <Typography paragraph sx={{ pl: 2 }}>
                    {answers[q.id] || 'No answer provided'}
                  </Typography>
                </Box>
              ))}
              
              <Button 
                variant="contained" 
                color="primary" 
                onClick={restartInterview}
                sx={{ mt: 2 }}
              >
                Start New Interview
              </Button>
            </Paper>
          )}
        </>
      )}
      
      {(recognitionError || synthesisError) && (
        <Chip 
          color="error" 
          label={recognitionError || synthesisError} 
          sx={{ mt: 2 }}
        />
      )}
    </Box>
  );
};

export default Interview;
