import { useState, useEffect, useCallback } from 'react';

// Custom hook for speech synthesis
const useSpeechSynthesis = () => {
  const [voices, setVoices] = useState([]);
  const [speaking, setSpeaking] = useState(false);
  const [error, setError] = useState(null);
  const [utterance, setUtterance] = useState(null);

  // Initialize speech synthesis
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Check browser support
    if (!window.speechSynthesis) {
      setError('Speech synthesis is not supported in this browser.');
      return;
    }

    // Get available voices
    const loadVoices = () => {
      const availableVoices = window.speechSynthesis.getVoices();
      setVoices(availableVoices);
    };

    loadVoices();
    
    // Chrome loads voices asynchronously
    if (window.speechSynthesis.onvoiceschanged !== undefined) {
      window.speechSynthesis.onvoiceschanged = loadVoices;
    }

    return () => {
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // Speak text
  const speak = useCallback((text, options = {}) => {
    if (!window.speechSynthesis) {
      setError('Speech synthesis is not supported in this browser.');
      return;
    }

    // Cancel any ongoing speech
    window.speechSynthesis.cancel();

    // Create a new utterance
    const newUtterance = new SpeechSynthesisUtterance(text);
    
    // Set voice (prefer a female voice if available)
    if (options.voice) {
      newUtterance.voice = options.voice;
    } else if (voices.length > 0) {
      // Try to find a female voice
      const femaleVoice = voices.find(voice => 
        voice.name.includes('female') || 
        voice.name.includes('Female') || 
        voice.name.includes('Samantha') ||
        voice.name.includes('Google US English Female')
      );
      
      if (femaleVoice) {
        newUtterance.voice = femaleVoice;
      }
    }

    // Set other options
    newUtterance.rate = options.rate || 1;
    newUtterance.pitch = options.pitch || 1;
    newUtterance.volume = options.volume || 1;

    // Set event handlers
    newUtterance.onstart = () => setSpeaking(true);
    newUtterance.onend = () => setSpeaking(false);
    newUtterance.onerror = (event) => {
      setError(`Speech synthesis error: ${event.error}`);
      setSpeaking(false);
    };

    // Store the utterance to prevent garbage collection
    setUtterance(newUtterance);

    // Start speaking
    window.speechSynthesis.speak(newUtterance);
  }, [voices]);

  // Stop speaking
  const cancel = useCallback(() => {
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      setSpeaking(false);
    }
  }, []);

  return {
    speak,
    cancel,
    speaking,
    voices,
    error,
  };
};

export default useSpeechSynthesis;
