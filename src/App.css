#root {
  width: 100%;
  margin: 0 auto;
}

/* Microphone animation */
@keyframes mic-pulse {
  0% {
    transform: scale(1);
    color: #f44336;
  }
  50% {
    transform: scale(1.2);
    color: #d32f2f;
  }
  100% {
    transform: scale(1);
    color: #f44336;
  }
}

.mic-active {
  animation: mic-pulse 1.5s infinite;
}

/* Transition for question changes */
.question-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}
.question-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}
.question-transition-exit {
  opacity: 1;
  transform: translateY(0);
}
.question-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .interview-container {
    padding: 1rem;
  }
}
