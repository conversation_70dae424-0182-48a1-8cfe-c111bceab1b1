export const exportInterviewToPDF = (interview, questionsWithResponses) => {
  // Create a simple text-based export since we don't have PDF library
  const content = generateInterviewReport(interview, questionsWithResponses)
  
  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `interview-${interview.id}-report.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

export const exportInterviewToJSON = (interview, questionsWithResponses) => {
  const data = {
    interview: {
      id: interview.id,
      date: interview.interview_date,
      status: interview.status
    },
    questions_and_responses: questionsWithResponses.map((q, index) => ({
      question_number: index + 1,
      question: q.question_text,
      response: q.interview_responses[0]?.user_response || 'No response provided'
    }))
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `interview-${interview.id}-data.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

const generateInterviewReport = (interview, questionsWithResponses) => {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const answeredQuestions = questionsWithResponses.filter(q => 
    q.interview_responses.length > 0 && q.interview_responses[0].user_response.trim()
  ).length

  let report = `INTERVIEW REPORT
================

Interview ID: ${interview.id}
Date: ${formatDate(interview.interview_date)}
Status: ${interview.status.toUpperCase()}
Questions Answered: ${answeredQuestions} of ${questionsWithResponses.length}

QUESTIONS AND RESPONSES
======================

`

  questionsWithResponses.forEach((question, index) => {
    const response = question.interview_responses[0]
    const hasResponse = response && response.user_response.trim()
    
    report += `${index + 1}. ${question.question_text}

Response: ${hasResponse ? response.user_response : 'No response provided'}

${'='.repeat(50)}

`
  })

  report += `
SUMMARY
=======

This interview session ${interview.status === 'completed' ? 'was completed' : 'is in progress'}.
${answeredQuestions} out of ${questionsWithResponses.length} questions were answered.

Generated on: ${new Date().toLocaleString()}
`

  return report
}
