import { useState } from 'react';
import { ThemeProvider, createTheme, CssBaseline, Container, Box } from '@mui/material';
import Welcome from './components/Welcome';
import Interview from './components/Interview';
import './App.css';

// Create a theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
});

function App() {
  const [interviewStarted, setInterviewStarted] = useState(false);
  const [userInfo, setUserInfo] = useState(null);

  const handleStartInterview = (info) => {
    setUserInfo(info);
    setInterviewStarted(true);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          {!interviewStarted ? (
            <Welcome onStart={handleStartInterview} />
          ) : (
            <Interview userInfo={userInfo} />
          )}
        </Box>
      </Container>
    </ThemeProvider>
  );
}

export default App;
