// Sample interview questions for different roles
export const interviewQuestions = {
  general: [
    {
      id: 1,
      question: "Tell me about yourself.",
      expectedTopics: ["background", "experience", "skills", "career goals"],
    },
    {
      id: 2,
      question: "What are your strengths and weaknesses?",
      expectedTopics: ["strengths", "weaknesses", "self-improvement"],
    },
    {
      id: 3,
      question: "Why do you want to work for this company?",
      expectedTopics: ["company knowledge", "career goals", "motivation"],
    },
    {
      id: 4,
      question: "Where do you see yourself in 5 years?",
      expectedTopics: ["career goals", "ambition", "planning"],
    },
    {
      id: 5,
      question: "Describe a challenging situation you faced at work and how you handled it.",
      expectedTopics: ["problem-solving", "conflict resolution", "stress management"],
    },
  ],
  technical: [
    {
      id: 1,
      question: "Explain your experience with React and front-end development.",
      expectedTopics: ["React", "JavaScript", "front-end", "projects"],
    },
    {
      id: 2,
      question: "How do you approach debugging a complex issue?",
      expectedTopics: ["debugging", "problem-solving", "tools", "methodology"],
    },
    {
      id: 3,
      question: "Describe your experience with version control systems.",
      expectedTopics: ["Git", "collaboration", "workflow"],
    },
    {
      id: 4,
      question: "How do you stay updated with the latest technologies?",
      expectedTopics: ["learning", "resources", "continuous improvement"],
    },
    {
      id: 5,
      question: "Explain a complex technical concept in simple terms.",
      expectedTopics: ["communication", "simplification", "technical knowledge"],
    },
  ],
  behavioral: [
    {
      id: 1,
      question: "Describe a time when you had to work with a difficult team member.",
      expectedTopics: ["teamwork", "conflict resolution", "communication"],
    },
    {
      id: 2,
      question: "How do you handle tight deadlines?",
      expectedTopics: ["time management", "prioritization", "stress management"],
    },
    {
      id: 3,
      question: "Tell me about a time you made a mistake and how you handled it.",
      expectedTopics: ["accountability", "learning", "problem-solving"],
    },
    {
      id: 4,
      question: "How do you handle feedback?",
      expectedTopics: ["receptiveness", "improvement", "self-awareness"],
    },
    {
      id: 5,
      question: "Describe a situation where you showed leadership.",
      expectedTopics: ["leadership", "initiative", "teamwork"],
    },
  ],
};

// Function to get random questions from a category
export const getRandomQuestions = (category, count = 3) => {
  const questions = interviewQuestions[category] || interviewQuestions.general;
  const shuffled = [...questions].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// Function to get a mix of questions from all categories
export const getMixedQuestions = (count = 5) => {
  const allCategories = Object.keys(interviewQuestions);
  const result = [];
  
  // Distribute questions evenly among categories
  const questionsPerCategory = Math.ceil(count / allCategories.length);
  
  allCategories.forEach(category => {
    const categoryQuestions = getRandomQuestions(category, questionsPerCategory);
    result.push(...categoryQuestions);
  });
  
  // Shuffle and limit to requested count
  return result.sort(() => 0.5 - Math.random()).slice(0, count);
};
